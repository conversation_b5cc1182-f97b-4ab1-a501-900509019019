import { describe, it, expect } from 'vitest';
import { ContentConverter } from '../src/utils/content-converter';
import { Markdown } from '../src/markdown';
import * as fs from 'fs';
import * as path from 'path';
import * as matter from 'gray-matter';

describe('Debug Post Creation', () => {
  it('should debug the full roundtrip conversion', async () => {
    // Read the fixture file
    const fixturePath = path.resolve('tests/fixtures/sync-content-test.md');
    const fixtureContent = fs.readFileSync(fixturePath, 'utf8');
    
    console.log('=== ORIGINAL FIXTURE CONTENT ===');
    console.log(fixtureContent);
    
    // Parse frontmatter and content using gray-matter
    const parsed = matter(fixtureContent);
    const frontMatter = parsed.data;
    const markdownContent = parsed.content;
    
    console.log('\n=== PARSED CONTENT ===');
    console.log('Frontmatter:', JSON.stringify(frontMatter, null, 2));
    console.log('\nMarkdown content:');
    console.log(markdownContent);
    
    // Use ContentConverter to create Ghost post data
    const postData = await ContentConverter.createGhostPostData(frontMatter, markdownContent);
    
    console.log('\n=== GENERATED GHOST POST DATA ===');
    console.log('Title:', postData.title);
    console.log('Slug:', postData.slug);
    console.log('Status:', postData.status);
    
    console.log('\n=== LEXICAL CONTENT ===');
    const lexicalDoc = JSON.parse(postData.lexical);
    console.log(JSON.stringify(lexicalDoc, null, 2));
    
    // Now let's try to convert it back to markdown to see what we get
    const parser = new Markdown();
    
    try {
      const result = await parser.lexicalToMarkdown(lexicalDoc);
      console.log('\n=== CONVERTED BACK TO MARKDOWN ===');
      console.log(result.data);
      
      // Check if it contains the expected content
      console.log('\n=== CONTENT CHECKS ===');
      console.log('Contains ```javascript:', result.data.includes('```javascript'));
      console.log('Contains return "Sync successful":', result.data.includes('return "Sync successful"'));
      console.log('Contains > [!info]:', result.data.includes('> [!info]'));
      console.log('Contains callout text:', result.data.includes('This is an info callout'));
      
      // Assertions to verify the content
      expect(result.data).toContain('```javascript');
      expect(result.data).toContain('return "Sync successful"');
      expect(result.data).toContain('> [!info]');
      expect(result.data).toContain('This is an info callout');
      
    } finally {
      parser.destroy();
    }
  });
});
