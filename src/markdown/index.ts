/**
 * Obsidian-Ghost Markdown Parser
 *
 * A clean, simple parser that converts between Markdown and Lexical
 * with support for Obsidian-specific formatting and Ghost-specific nodes.
 */

import { createHeadlessEditor } from '@lexical/headless';
import type { LexicalEditor } from 'lexical';
import { $createTextNode, $createParagraphNode, ElementNode, type NodeKey, type EditorConfig, $applyNodeReplacement } from 'lexical';
import { $convertFromMarkdownString, $convertToMarkdownString } from '@lexical/markdown';
import { TRANSFORMERS, type TextFormatTransformer, type ElementTransformer, type TextMatchTransformer, type MultilineElementTransformer } from '@lexical/markdown';

import { HeadingNode, QuoteNode, $createQuoteNode, $isQuoteNode } from '@lexical/rich-text';
import { ListItemNode, ListNode } from '@lexical/list';
import { CodeNode } from '@lexical/code';
import { LinkNode, $createLinkNode, $isLinkNode } from '@lexical/link';
import { TableNode, TableRowNode, TableCellNode, $createTableNode, $createTableRowNode, $createTableCellNode, $isTableNode } from '@lexical/table';

/**
 * Custom HorizontalRuleNode for handling horizontal rules in markdown
 */
export class HorizontalRuleNode extends ElementNode {
  static getType(): string {
    return 'horizontalrule';
  }

  static clone(node: HorizontalRuleNode): HorizontalRuleNode {
    return new HorizontalRuleNode(node.__key);
  }

  constructor(key?: NodeKey) {
    super(key);
  }

  createDOM(_config: EditorConfig): HTMLElement {
    const element = document.createElement('hr');
    return element;
  }

  updateDOM(_prevNode: this, _dom: HTMLElement, _config: EditorConfig): boolean {
    return false;
  }

  static importJSON(): HorizontalRuleNode {
    return $createHorizontalRuleNode();
  }

  exportJSON() {
    return {
      ...super.exportJSON(),
      type: 'horizontalrule',
      version: 1,
    };
  }

  canBeEmpty(): boolean {
    return true;
  }

  isInline(): boolean {
    return false;
  }

  canInsertTextBefore(): boolean {
    return false;
  }

  canInsertTextAfter(): boolean {
    return false;
  }
}

export function $createHorizontalRuleNode(): HorizontalRuleNode {
  return $applyNodeReplacement(new HorizontalRuleNode());
}

export function $isHorizontalRuleNode(node: any | null | undefined): node is HorizontalRuleNode {
  return node instanceof HorizontalRuleNode;
}

/**
 * Horizontal rule transformer for markdown horizontal rules
 * Converts --- and *** syntax to horizontal rule nodes
 */
export const HORIZONTAL_RULE: ElementTransformer = {
  dependencies: [HorizontalRuleNode],
  export: (node, _exportChildren) => {
    if (!$isHorizontalRuleNode(node)) {
      return null;
    }
    return '---';
  },
  regExp: /^(?:---|\*\*\*|___)\s*$/,
  replace: (parentNode, _children, _match, _isImport) => {
    const horizontalRuleNode = $createHorizontalRuleNode();
    parentNode.replace(horizontalRuleNode);
  },
  type: 'element',
};

/**
 * Obsidian-specific italic transformer for asterisk syntax
 * Enhanced version that handles Obsidian's specific italic behavior
 */
export const OBSIDIAN_ITALIC_STAR: TextFormatTransformer = {
  format: ['italic'],
  tag: '*',
  type: 'text-format',
};

/**
 * Obsidian-specific italic transformer for underscore syntax
 * Enhanced version that handles Obsidian's specific italic behavior
 */
export const OBSIDIAN_ITALIC_UNDERSCORE: TextFormatTransformer = {
  format: ['italic'],
  intraword: false, // Obsidian doesn't allow underscores within words for italic
  tag: '_',
  type: 'text-format',
};

/**
 * Ghost callout transformer for Obsidian-style callouts
 * Converts > [!type] syntax to Ghost callout cards
 */
export const GHOST_CALLOUT: ElementTransformer = {
  dependencies: [QuoteNode],
  export: (node, exportChildren) => {
    if (!$isQuoteNode(node)) {
      return null;
    }

    // Check if this is a callout by looking at the first child
    const firstChild = node.getFirstChild();
    if (firstChild && firstChild.getType() === 'paragraph') {
      const textContent = firstChild.getTextContent();
      const calloutMatch = textContent.match(/^\[!([^\]]+)\]/);
      if (calloutMatch) {
        const calloutType = calloutMatch[1].toLowerCase();
        const content = exportChildren(node);
        // Remove the callout marker from the content
        const cleanContent = content.replace(/^\[![^\]]+\]\s*/, '');
        return `> [!${calloutType}]\n> ${cleanContent.replace(/\n/g, '\n> ')}`;
      }
    }

    return null; // Let the default quote transformer handle it
  },
  regExp: /^>\s*\[!([^\]]+)\]/,
  replace: (parentNode, children, match, isImport) => {
    const calloutType = match[1];
    const node = $createQuoteNode();

    // Create a paragraph with the callout marker
    const markerParagraph = $createParagraphNode();
    const markerText = $createTextNode(`[!${calloutType}]`);
    markerParagraph.append(markerText);

    // Add the marker paragraph and then the children
    node.append(markerParagraph, ...children);
    parentNode.replace(node);

    if (!isImport) {
      node.select(0, 0);
    }
  },
  type: 'element',
};

/**
 * Obsidian wikilink transformer
 * Converts [[target|alias]] syntax to regular links
 */
export const OBSIDIAN_WIKILINK: TextMatchTransformer = {
  dependencies: [LinkNode],
  export: (node, exportChildren, exportFormat) => {
    if (!$isLinkNode(node)) {
      return null;
    }

    // Check if this is a wikilink (has special data attribute or URL pattern)
    const url = node.getURL();
    if (url.startsWith('obsidian://') || node.getTitle()?.includes('wikilink')) {
      const textContent = exportChildren(node);
      const target = url.replace('obsidian://', '');
      return textContent !== target ? `[[${target}|${textContent}]]` : `[[${target}]]`;
    }

    return null; // Let the default link transformer handle it
  },
  importRegExp: /\[\[([^\]|]+)(\|([^\]]+))?\]\]/,
  regExp: /\[\[([^\]|]+)(\|([^\]]+))?\]\]/,
  replace: (textNode, match) => {
    const target = match[1];
    const alias = match[3];
    const linkNode = $createLinkNode(`obsidian://${target}`, {
      title: 'wikilink'
    });
    const textNode2 = $createTextNode(alias || target);
    linkNode.append(textNode2);
    textNode.replace(linkNode);
  },
  trigger: ']]',
  type: 'text-match',
};

/**
 * Obsidian inline math transformer
 * Converts $equation$ syntax to math nodes (represented as code for now)
 */
export const OBSIDIAN_MATH_INLINE: TextMatchTransformer = {
  dependencies: [CodeNode],
  export: (node, exportChildren, exportFormat) => {
    // For now, we'll represent math as inline code with a special marker
    if (node.getType() === 'code' && (node as any).__mathInline) {
      return `$${(node as any).getTextContent()}$`;
    }
    return null;
  },
  importRegExp: /\$([^$\n]+)\$/,
  regExp: /\$([^$\n]+)\$$/,
  replace: (textNode, match) => {
    const equation = match[1];
    // Create a code node to represent the math (since we don't have a dedicated math node)
    const codeNode = $createTextNode(equation);
    // Mark it as math for export
    (codeNode as any).__mathInline = true;
    textNode.replace(codeNode);
  },
  trigger: '$',
  type: 'text-match',
};

/**
 * Obsidian tags transformer
 * Converts #tag syntax to special link nodes
 */
export const OBSIDIAN_TAGS: TextMatchTransformer = {
  dependencies: [LinkNode],
  export: (node, exportChildren, exportFormat) => {
    if (!$isLinkNode(node)) {
      return null;
    }

    // Check if this is a tag link
    const url = node.getURL();
    if (url.startsWith('obsidian://tag/')) {
      const tag = url.replace('obsidian://tag/', '');
      return `#${tag}`;
    }

    return null;
  },
  importRegExp: /#([a-zA-Z0-9_-]+)/,
  regExp: /#([a-zA-Z0-9_-]+)$/,
  replace: (textNode, match) => {
    const tag = match[1];
    const linkNode = $createLinkNode(`obsidian://tag/${tag}`, {
      title: 'tag'
    });
    const textNode2 = $createTextNode(`#${tag}`);
    linkNode.append(textNode2);
    textNode.replace(linkNode);
  },
  trigger: '#',
  type: 'text-match',
};

/**
 * Obsidian table transformer
 * Converts markdown table syntax to Lexical table nodes
 */
export const OBSIDIAN_TABLE: MultilineElementTransformer = {
  dependencies: [TableNode, TableRowNode, TableCellNode],
  export: (node, exportChildren) => {
    if (!$isTableNode(node)) {
      return null;
    }

    const rows = node.getChildren();
    if (rows.length === 0) {
      return null;
    }

    const tableLines: string[] = [];

    rows.forEach((row, rowIndex) => {
      if (row.getType() === 'tablerow') {
        const cells = (row as any).getChildren();
        const cellTexts = cells.map((cell: any) => {
          if (cell.getType() === 'tablecell') {
            return exportChildren(cell).trim();
          }
          return '';
        });

        const rowText = `| ${cellTexts.join(' | ')} |`;
        tableLines.push(rowText);

        // Add separator row after header (first row)
        if (rowIndex === 0) {
          const separator = `|${cellTexts.map(() => '----------|').join('')}`;
          tableLines.push(separator);
        }
      }
    });

    return tableLines.join('\n');
  },
  regExpStart: /^\|(.+)\|$/,
  regExpEnd: { optional: true, regExp: /^(?!\|)/ },
  replace: (rootNode, _children, startMatch, _endMatch, linesInBetween, isImport) => {
    if (!isImport) {
      return;
    }

    // Parse table lines
    const lines = [startMatch[0], ...linesInBetween];
    const tableLines = lines.filter(line => line.trim().startsWith('|') && line.trim().endsWith('|'));

    if (tableLines.length < 2) {
      return; // Need at least header and separator
    }

    // Skip separator line (usually the second line with dashes)
    const dataLines = tableLines.filter((line, index) => {
      if (index === 1) {
        // Check if this is a separator line
        return !line.match(/^\|[\s\-\|:]+\|$/);
      }
      return true;
    });

    if (dataLines.length === 0) {
      return;
    }

    const tableNode = $createTableNode();

    dataLines.forEach((line, rowIndex) => {
      const cells = line.split('|').slice(1, -1); // Remove empty first and last elements
      const rowNode = $createTableRowNode();

      cells.forEach(cellText => {
        const cellNode = $createTableCellNode(rowIndex === 0 ? 1 : 0); // 1 for header, 0 for data
        const paragraphNode = $createParagraphNode();
        const textNode = $createTextNode(cellText.trim());
        paragraphNode.append(textNode);
        cellNode.append(paragraphNode);
        rowNode.append(cellNode);
      });

      tableNode.append(rowNode);
    });

    rootNode.append(tableNode);
  },
  type: 'multiline-element',
};

/**
 * Combined transformers including Obsidian-specific ones
 */
const OBSIDIAN_TRANSFORMERS = [
  // Start with default transformers, but exclude the default italic transformers
  // We keep STRIKETHROUGH, BOLD, INLINE_CODE, etc. but replace italic transformers
  ...TRANSFORMERS.filter(t => {
    if (t.type !== 'text-format') return true;
    const tag = (t as any).tag;
    // Exclude default italic transformers (ITALIC_STAR and ITALIC_UNDERSCORE)
    return tag !== '*' && tag !== '_';
  }),
  // Add our custom text format transformers
  OBSIDIAN_ITALIC_STAR,
  OBSIDIAN_ITALIC_UNDERSCORE,
  // Add our custom element and text-match transformers
  GHOST_CALLOUT,
  HORIZONTAL_RULE,
  OBSIDIAN_TABLE,
  OBSIDIAN_WIKILINK,
  OBSIDIAN_MATH_INLINE,
  OBSIDIAN_TAGS,
];

/**
 * Result of a conversion operation
 */
export interface ConversionResult<T> {
  success: boolean;
  data?: T;
  error?: string;
  warnings?: string[];
}

/**
 * Lexical document representation
 */
export interface LexicalDocument {
  root: any; // Use any for compatibility with Lexical's serialized format
  nodes?: any[]; // For compatibility with original parser
}

/**
 * Base Lexical node interface
 */
export interface LexicalNode {
  type: string;
  version?: number;
  [key: string]: any;
}

/**
 * Lexical root node
 */
export interface LexicalRootNode extends LexicalNode {
  type: 'root';
  children: LexicalNode[];
  direction?: 'ltr' | 'rtl';
  format?: string;
  indent?: number;
  version?: number;
}

/**
 * Lexical text node
 */
export interface LexicalTextNode extends LexicalNode {
  type: 'text';
  text: string;
  detail: number;
  format: number;
  mode: 'normal' | 'token' | 'segmented';
  style: string;
}

/**
 * Lexical paragraph node
 */
export interface LexicalParagraphNode extends LexicalNode {
  type: 'paragraph';
  children: LexicalNode[];
  direction?: 'ltr' | 'rtl';
  format?: string;
  indent?: number;
}

/**
 * Lexical heading node
 */
export interface LexicalHeadingNode extends LexicalNode {
  type: 'heading';
  tag: 'h1' | 'h2' | 'h3' | 'h4' | 'h5' | 'h6';
  children: LexicalNode[];
  direction?: 'ltr' | 'rtl';
  format?: string;
  indent?: number;
}

/**
 * Lexical list node
 */
export interface LexicalListNode extends LexicalNode {
  type: 'list';
  listType: 'bullet' | 'number' | 'check';
  children: LexicalListItemNode[];
  start?: number;
  tag: 'ul' | 'ol';
}

/**
 * Lexical list item node
 */
export interface LexicalListItemNode extends LexicalNode {
  type: 'listitem';
  children: LexicalNode[];
  checked?: boolean;
  value?: number;
}

/**
 * Text formatting constants - aligned with official Lexical format
 */
export const TEXT_FORMAT = {
  BOLD: 1,           // IS_BOLD = 1
  ITALIC: 2,         // IS_ITALIC = 1 << 1
  STRIKETHROUGH: 4,  // IS_STRIKETHROUGH = 1 << 2
  UNDERLINE: 8,      // IS_UNDERLINE = 1 << 3
  CODE: 16,          // IS_CODE = 1 << 4
  SUBSCRIPT: 32,     // IS_SUBSCRIPT = 1 << 5
  SUPERSCRIPT: 64,   // IS_SUPERSCRIPT = 1 << 6
} as const;

export type TextFormat = typeof TEXT_FORMAT[keyof typeof TEXT_FORMAT];

/**
 * Conversion options
 */
export interface ConversionOptions {
  preserveUnknownNodes?: boolean;
  enableGhostFeatures?: boolean;
  fallbackToHTML?: boolean;
  validateInput?: boolean;
  maxRetries?: number;
}

/**
 * Error codes for different types of conversion failures
 */
export enum ErrorCode {
  NULL_INPUT = 'NULL_INPUT',
  INVALID_INPUT = 'INVALID_INPUT',
  PARSE_FAILED = 'PARSE_FAILED',
  CONVERSION_FAILED = 'CONVERSION_FAILED',
  VALIDATION_FAILED = 'VALIDATION_FAILED',
  UNKNOWN_ERROR = 'UNKNOWN_ERROR'
}

/**
 * Enhanced conversion result with error codes and warnings
 */
export interface EnhancedConversionResult<T> extends ConversionResult<T> {
  errorCode?: ErrorCode;
  warnings?: string[];
  metadata?: {
    processingTime?: number;
    nodeCount?: number;
    retryCount?: number;
  };
}

/**
 * Main Markdown parser class
 */
export class Markdown {
  private editor: LexicalEditor;

  constructor() {
    // Create a headless Lexical editor (no DOM required)
    // Register all the node types we need for markdown parsing
    this.editor = createHeadlessEditor({
      namespace: 'obsidian-ghost-markdown',
      nodes: [
        HeadingNode,
        ListNode,
        ListItemNode,
        QuoteNode,
        CodeNode,
        LinkNode,
        TableNode,
        TableRowNode,
        TableCellNode,
        HorizontalRuleNode,
      ],
      onError: (error) => {
        console.error('Lexical editor error:', error);
      },
    });
  }

  /**
   * Convert markdown string to Lexical document
   */
  async markdownToLexical(
    markdown: string,
    options: ConversionOptions = {}
  ): Promise<EnhancedConversionResult<LexicalDocument>> {
    const startTime = Date.now();
    const warnings: string[] = [];

    try {
      // Input validation
      if (markdown === null || markdown === undefined) {
        return {
          success: false,
          error: 'Input cannot be null or undefined',
          errorCode: ErrorCode.NULL_INPUT,
          warnings,
        };
      }

      if (typeof markdown !== 'string') {
        return {
          success: false,
          error: 'Input must be a string',
          errorCode: ErrorCode.INVALID_INPUT,
          warnings,
        };
      }

      // Handle empty or whitespace-only content
      if (!markdown.trim()) {
        return {
          success: true,
          data: {
            root: {
              type: 'root',
              children: [],
              direction: 'ltr',
              format: '',
              indent: 0,
              version: 1
            },
            nodes: []
          },
          warnings,
          metadata: {
            processingTime: Date.now() - startTime,
            nodeCount: 0,
            retryCount: 0,
          }
        };
      }

      // Validate input if requested
      if (options.validateInput) {
        const validationResult = this.validateMarkdownInput(markdown);
        if (!validationResult.isValid) {
          warnings.push(...validationResult.warnings);
          if (validationResult.isCritical) {
            return {
              success: false,
              error: `Input validation failed: ${validationResult.warnings.join(', ')}`,
              errorCode: ErrorCode.VALIDATION_FAILED,
              warnings,
            };
          }
        }
      }

      // Use the same pattern as Lexical tests
      this.editor.update(
        () => {
          $convertFromMarkdownString(markdown, OBSIDIAN_TRANSFORMERS);
        },
        {
          discrete: true,
        }
      );

      // Get the editor state after the update
      const editorState = this.editor.getEditorState();
      const editorStateJSON = editorState.toJSON();

      // Validate the resulting document structure
      if (!editorStateJSON.root || editorStateJSON.root.type !== 'root') {
        return {
          success: false,
          error: 'Invalid document structure: Root node must have type "root"',
          errorCode: ErrorCode.CONVERSION_FAILED,
          warnings,
        };
      }

      // Fix the Lexical structure to match Ghost's expected format
      const fixedRoot = this.fixLexicalStructureForGhost(editorStateJSON.root);

      const document: LexicalDocument = {
        root: fixedRoot,
        nodes: [], // For compatibility with original parser
      };

      // Count nodes for metadata
      const nodeCount = this.countNodes(document.root);

      return {
        success: true,
        data: document,
        warnings,
        metadata: {
          processingTime: Date.now() - startTime,
          nodeCount,
          retryCount: 0,
        }
      };
    } catch (error) {
      return {
        success: false,
        error: error instanceof Error ? error.message : 'Unknown error',
        errorCode: ErrorCode.UNKNOWN_ERROR,
        warnings,
        metadata: {
          processingTime: Date.now() - startTime,
          retryCount: 0,
        }
      };
    }
  }

  /**
   * Convert Lexical document to markdown string
   */
  async lexicalToMarkdown(
    document: LexicalDocument,
    options: ConversionOptions = {}
  ): Promise<EnhancedConversionResult<string>> {
    const startTime = Date.now();
    const warnings: string[] = [];



    // Note: options parameter is reserved for future use

    try {
      // Input validation
      if (!document) {
        return {
          success: false,
          error: 'Document cannot be null or undefined',
          errorCode: ErrorCode.NULL_INPUT,
          warnings,
        };
      }

      if (!document.root) {
        return {
          success: false,
          error: 'Invalid document structure: Root node must have type "root"',
          errorCode: ErrorCode.INVALID_INPUT,
          warnings,
        };
      }

      // Validate document structure
      const validationResult = this.validateLexicalDocument(document);
      if (!validationResult.isValid) {
        warnings.push(...validationResult.warnings);
        if (validationResult.isCritical) {
          return {
            success: false,
            error: `Document validation failed: ${validationResult.warnings.join(', ')}`,
            errorCode: ErrorCode.VALIDATION_FAILED,
            warnings,
          };
        }
      }

      // Handle empty documents
      if (!document.root.children || document.root.children.length === 0) {
        return {
          success: true,
          data: '',
          warnings,
          metadata: {
            processingTime: Date.now() - startTime,
            nodeCount: 1, // Just the root node
            retryCount: 0,
          }
        };
      }

      // Preprocess Ghost-specific node types before passing to Lexical
      // This must happen BEFORE parseEditorState to avoid Lexical errors
      const processedDocument = this.preprocessGhostNodes(document);

      // Restore the editor state from the document
      // parseEditorState expects the full editor state JSON, not just the root
      const fullEditorStateJSON = JSON.stringify({
        root: processedDocument.root
      });

      const editorState = this.editor.parseEditorState(fullEditorStateJSON);
      this.editor.setEditorState(editorState);

      // Convert to markdown using the same pattern as Lexical tests
      const markdown = this.editor.getEditorState().read(() => {
        return $convertToMarkdownString(OBSIDIAN_TRANSFORMERS);
      });

      const nodeCount = this.countNodes(document.root);

      return {
        success: true,
        data: markdown,
        warnings,
        metadata: {
          processingTime: Date.now() - startTime,
          nodeCount,
          retryCount: 0,
        }
      };
    } catch (error) {
      return {
        success: false,
        error: error instanceof Error ? error.message : 'Unknown error',
        errorCode: ErrorCode.UNKNOWN_ERROR,
        warnings,
        metadata: {
          processingTime: Date.now() - startTime,
          retryCount: 0,
        }
      };
    }
  }

  /**
   * Validate markdown input
   * @private
   */
  private validateMarkdownInput(markdown: string): {
    isValid: boolean;
    warnings: string[];
    isCritical: boolean;
  } {
    const warnings: string[] = [];
    let isCritical = false;

    // Check for null bytes and control characters
    if (/[\x00-\x08\x0B\x0C\x0E-\x1F\x7F]/.test(markdown)) {
      warnings.push('Input contains null bytes or control characters');
    }

    // Check for extremely long lines that might cause performance issues
    const lines = markdown.split('\n');
    const maxLineLength = 10000;
    for (let i = 0; i < lines.length; i++) {
      if (lines[i].length > maxLineLength) {
        warnings.push(`Line ${i + 1} is extremely long (${lines[i].length} characters)`);
      }
    }

    // Check for very large documents
    if (markdown.length > 1000000) { // 1MB
      warnings.push('Document is very large and may cause performance issues');
    }

    return {
      isValid: !isCritical,
      warnings,
      isCritical,
    };
  }

  /**
   * Validate lexical document structure
   * @private
   */
  private validateLexicalDocument(document: LexicalDocument): {
    isValid: boolean;
    warnings: string[];
    isCritical: boolean;
  } {
    const warnings: string[] = [];
    let isCritical = false;

    // Check root node
    if (!document.root || document.root.type !== 'root') {
      warnings.push('Root node must have type "root"');
      isCritical = true;
    }

    // Check for valid children structure
    if (document.root.children && Array.isArray(document.root.children)) {
      for (let i = 0; i < document.root.children.length; i++) {
        const child = document.root.children[i];
        if (!child || typeof child.type !== 'string') {
          warnings.push(`Invalid node at position ${i}: missing or invalid type`);
          isCritical = true;
        }
      }
    }

    return {
      isValid: !isCritical,
      warnings,
      isCritical,
    };
  }

  /**
   * Fix Lexical structure to match Ghost's expected format
   * This addresses differences between what Lexical generates and what Ghost expects
   * @private
   */
  private fixLexicalStructureForGhost(node: any): any {
    if (!node || typeof node !== 'object') {
      return node;
    }

    // Create a copy to avoid mutating the original
    const fixed = { ...node };

    // Convert table nodes to markdown cards (Ghost doesn't support native table nodes)
    if (fixed.type === 'table') {
      return this.convertTableToMarkdownCard(fixed);
    }

    // Fix direction: Ghost expects "ltr" instead of null
    if (fixed.direction === null) {
      fixed.direction = "ltr";
    }

    // Convert node types to Ghost's expected format
    if (fixed.type === 'text') {
      fixed.type = 'extended-text';
    } else if (fixed.type === 'heading') {
      fixed.type = 'extended-heading';
    } else if (fixed.type === 'code') {
      // Convert Lexical code nodes to Ghost codeblock format
      return this.convertCodeToCodeblock(fixed);
    } else if (fixed.type === 'quote') {
      // Check if this is a callout and convert to Ghost callout format
      const calloutResult = this.convertQuoteToCallout(fixed);
      if (calloutResult) {
        return calloutResult;
      }
    }

    // Remove extra properties that Ghost doesn't expect
    if (fixed.type === 'paragraph') {
      delete fixed.textFormat;
      delete fixed.textStyle;
    }

    // Recursively fix children
    if (fixed.children && Array.isArray(fixed.children)) {
      fixed.children = fixed.children.map((child: any) =>
        this.fixLexicalStructureForGhost(child)
      );
    }

    return fixed;
  }

  /**
   * Convert a table node to a Ghost markdown card
   * @private
   */
  private convertTableToMarkdownCard(tableNode: any): any {
    try {
      // Manually extract table content and convert to markdown
      const rows = tableNode.children || [];
      if (rows.length === 0) {
        throw new Error('No table rows found');
      }

      const tableLines: string[] = [];

      rows.forEach((row: any, rowIndex: number) => {
        if (row.type === 'tablerow') {
          const cells = row.children || [];
          const cellTexts = cells.map((cell: any) => {
            if (cell.type === 'tablecell') {
              // Extract text from cell children
              const paragraphs = cell.children || [];
              return paragraphs.map((para: any) => {
                if (para.type === 'paragraph' && para.children) {
                  return para.children.map((textNode: any) => textNode.text || '').join('');
                }
                return para.text || '';
              }).join('');
            }
            return '';
          });

          const rowText = `| ${cellTexts.join(' | ')} |`;
          tableLines.push(rowText);

          // Add separator row after header (first row)
          if (rowIndex === 0) {
            const separator = `|${cellTexts.map(() => '----------|').join('')}`;
            tableLines.push(separator);
          }
        }
      });

      const markdownContent = tableLines.join('\n');

      if (!markdownContent || markdownContent.trim() === '') {
        throw new Error('Empty markdown content generated');
      }

      // Return a Ghost markdown card
      return {
        type: 'markdown',
        version: 1,
        markdown: markdownContent
      };
    } catch (error) {
      console.warn('Table conversion failed:', error);
      // Fallback: create a simple paragraph if table conversion fails
      return {
        type: 'paragraph',
        children: [{
          type: 'extended-text',
          text: '[Table content could not be converted]',
          detail: 0,
          format: 0,
          mode: 'normal',
          style: '',
          version: 1
        }],
        direction: 'ltr',
        format: '',
        indent: 0,
        version: 1
      };
    }
  }

  /**
   * Count nodes in a document tree
   * @private
   */
  private countNodes(node: any): number {
    let count = 1; // Count the current node
    if (node.children && Array.isArray(node.children)) {
      for (const child of node.children) {
        count += this.countNodes(child);
      }
    }
    return count;
  }

  /**
   * Preprocess Ghost-specific node types to convert them to standard Lexical nodes
   */
  private preprocessGhostNodes(document: LexicalDocument): LexicalDocument {
    const processedDocument = JSON.parse(JSON.stringify(document)); // Deep clone

    if (processedDocument.root && processedDocument.root.children) {
      processedDocument.root.children = this.processNodeArray(processedDocument.root.children);
    }

    return processedDocument;
  }

  /**
   * Process an array of nodes, converting Ghost-specific types
   */
  private processNodeArray(nodes: any[]): any[] {
    return nodes.map(node => this.processNode(node)).filter(node => node !== null);
  }

  /**
   * Process a single node, converting Ghost-specific types to standard Lexical nodes
   */
  private processNode(node: any): any {
    if (!node || typeof node !== 'object') {
      return node;
    }



    // Handle Ghost callout nodes
    if (node.type === 'callout') {
      return this.convertGhostCalloutToQuote(node);
    }

    // Handle Ghost markdown nodes
    if (node.type === 'markdown') {
      return this.convertGhostMarkdownToParagraph(node);
    }

    // Handle Ghost extended-heading nodes
    if (node.type === 'extended-heading') {
      return this.convertExtendedHeading(node);
    }

    // Handle Ghost extended-text nodes
    if (node.type === 'extended-text') {
      return this.convertExtendedText(node);
    }

    // Handle codeblock nodes (convert to standard code nodes)
    if (node.type === 'codeblock') {
      return this.convertCodeblockToCode(node);
    }

    // Handle horizontalrule nodes (already compatible with Lexical)
    if (node.type === 'horizontalrule') {
      return {
        type: 'horizontalrule',
        version: node.version || 1
      };
    }

    // Process children recursively
    if (node.children && Array.isArray(node.children)) {
      node.children = this.processNodeArray(node.children);
    }

    return node;
  }

  /**
   * Convert Ghost callout node to Obsidian-style quote node with callout formatting
   */
  private convertGhostCalloutToQuote(calloutNode: any): any {
    // Extract text content from HTML
    const textContent = this.extractTextFromHTML(calloutNode.calloutText || '');

    // Map emoji back to callout type
    const calloutType = this.getCalloutTypeFromEmoji(calloutNode.calloutEmoji || '💡');

    // Create a quote node with proper Obsidian callout structure
    return {
      type: 'quote',
      children: [
        {
          type: 'text',
          text: `[!${calloutType}]`,
          detail: 0,
          format: 0,
          mode: 'normal',
          style: '',
          version: 1
        },
        {
          type: 'linebreak',
          version: 1
        },
        {
          type: 'text',
          text: textContent,
          detail: 0,
          format: 0,
          mode: 'normal',
          style: '',
          version: 1
        }
      ],
      direction: 'ltr',
      format: '',
      indent: 0,
      version: 1
    };
  }

  /**
   * Map Ghost callout emoji back to Obsidian callout type
   */
  private getCalloutTypeFromEmoji(emoji: string): string {
    const emojiToType: Record<string, string> = {
      '📝': 'note',
      'ℹ️': 'info',
      '💡': 'tip',
      '✅': 'success',
      '⚠️': 'warning',
      '❌': 'danger',
      '🚨': 'error',
      '❓': 'question',
      '💬': 'quote',
      '📋': 'example',
      '📄': 'abstract',
      '☑️': 'todo',
      '🐛': 'bug'
    };

    return emojiToType[emoji] || 'note';
  }

  /**
   * Convert Ghost markdown node to paragraph with the raw markdown content
   */
  private convertGhostMarkdownToParagraph(markdownNode: any): any {
    // For now, convert markdown tables to plain text representation
    // In the future, we could parse the markdown and convert to proper table nodes
    const markdownContent = markdownNode.markdown || '';

    return {
      type: 'paragraph',
      children: [
        {
          type: 'text',
          text: markdownContent,
          detail: 0,
          format: 16, // Code format to preserve table structure
          mode: 'normal',
          style: '',
          version: 1
        }
      ],
      direction: 'ltr',
      format: '',
      indent: 0,
      version: 1
    };
  }

  /**
   * Convert Ghost extended-heading to standard heading
   */
  private convertExtendedHeading(extendedHeading: any): any {
    return {
      type: 'heading',
      tag: extendedHeading.tag || 'h1',
      children: extendedHeading.children ? this.processNodeArray(extendedHeading.children) : [],
      direction: extendedHeading.direction || 'ltr',
      format: extendedHeading.format || '',
      indent: extendedHeading.indent || 0,
      version: extendedHeading.version || 1
    };
  }

  /**
   * Convert Ghost extended-text to standard text
   */
  private convertExtendedText(extendedText: any): any {
    return {
      type: 'text',
      text: extendedText.text || '',
      detail: extendedText.detail || 0,
      format: extendedText.format || 0,
      mode: extendedText.mode || 'normal',
      style: extendedText.style || '',
      version: extendedText.version || 1
    };
  }

  /**
   * Convert Ghost codeblock nodes to standard Lexical code nodes
   * Ghost structure: {"type": "codeblock", "code": "...", "language": "elixir", "caption": ""}
   * Lexical structure: {"type": "code", "children": [...], "language": "elixir"}
   */
  private convertCodeblockToCode(codeblock: any): any {
    // Ghost stores code content in the "code" property as a string
    // Lexical expects it as children with regular text nodes (not extended-text)
    const codeContent = codeblock.code || '';

    return {
      type: 'code',
      children: [
        {
          type: 'text',
          text: codeContent,
          detail: 0,
          format: 0,
          mode: 'normal',
          style: '',
          version: 1
        }
      ],
      direction: codeblock.direction || 'ltr',
      format: codeblock.format || '',
      indent: codeblock.indent || 0,
      language: codeblock.language || '',
      version: codeblock.version || 1
    };
  }

  /**
   * Convert standard Lexical code nodes to Ghost codeblock format
   * Lexical structure: {"type": "code", "children": [...], "language": "javascript"}
   * Ghost structure: {"type": "codeblock", "code": "...", "language": "javascript", "caption": ""}
   */
  private convertCodeToCodeblock(codeNode: any): any {
    // Extract text content from children
    let codeContent = '';
    if (codeNode.children && Array.isArray(codeNode.children)) {
      codeContent = codeNode.children
        .map((child: any) => child.text || '')
        .join('');
    }

    return {
      type: 'codeblock',
      version: codeNode.version || 1,
      code: codeContent,
      language: codeNode.language || '',
      caption: '' // Ghost expects this property
    };
  }

  /**
   * Convert quote nodes to Ghost callout format if they contain callout syntax
   * Obsidian callout: > [!info]\n> This is an info callout
   * Ghost callout: {"type": "callout", "calloutText": "<p>...</p>", "calloutEmoji": "ℹ️", "backgroundColor": "blue"}
   */
  private convertQuoteToCallout(quoteNode: any): any | null {
    if (!quoteNode.children || !Array.isArray(quoteNode.children)) {
      return null;
    }

    // Look for callout marker in the first text node
    const firstChild = quoteNode.children[0];
    if (!firstChild || (firstChild.type !== 'extended-text' && firstChild.type !== 'text')) {
      return null;
    }

    const calloutMatch = firstChild.text?.match(/^\[!([^\]]+)\]$/);
    if (!calloutMatch) {
      return null;
    }

    const calloutType = calloutMatch[1].toLowerCase();

    // Extract the content (skip the marker and linebreak)
    const contentNodes = quoteNode.children.slice(2); // Skip marker and linebreak
    const contentText = contentNodes
      .map((node: any) => node.text || '')
      .join('')
      .trim();

    // Map callout types to emojis and colors
    const calloutConfig = this.getCalloutConfig(calloutType);

    return {
      type: 'callout',
      version: 1,
      calloutText: `<p dir="ltr"><span style="white-space: pre-wrap;">${contentText}</span></p>`,
      calloutEmoji: calloutConfig.emoji,
      backgroundColor: calloutConfig.backgroundColor
    };
  }

  /**
   * Get emoji and background color for callout types
   */
  private getCalloutConfig(calloutType: string): { emoji: string; backgroundColor: string } {
    const configs: Record<string, { emoji: string; backgroundColor: string }> = {
      'note': { emoji: '📝', backgroundColor: 'grey' },
      'info': { emoji: 'ℹ️', backgroundColor: 'blue' },
      'tip': { emoji: '💡', backgroundColor: 'green' },
      'success': { emoji: '✅', backgroundColor: 'green' },
      'warning': { emoji: '⚠️', backgroundColor: 'yellow' },
      'danger': { emoji: '❌', backgroundColor: 'red' },
      'error': { emoji: '🚨', backgroundColor: 'red' },
      'question': { emoji: '❓', backgroundColor: 'blue' },
      'quote': { emoji: '💬', backgroundColor: 'grey' },
      'example': { emoji: '📋', backgroundColor: 'purple' },
      'abstract': { emoji: '📄', backgroundColor: 'grey' },
      'todo': { emoji: '☑️', backgroundColor: 'blue' },
      'bug': { emoji: '🐛', backgroundColor: 'red' }
    };

    return configs[calloutType] || configs['note'];
  }

  /**
   * Convert text nodes to extended-text nodes
   * This handles the case where Ghost uses "text" instead of "extended-text"
   */
  private convertTextToExtendedText(text: any): any {
    return {
      type: 'extended-text',
      text: text.text || '',
      detail: text.detail || 0,
      format: text.format || 0,
      mode: text.mode || 'normal',
      style: text.style || '',
      version: text.version || 1
    };
  }

  /**
   * Extract plain text from HTML content
   */
  private extractTextFromHTML(html: string): string {
    // Simple HTML tag removal - in a real implementation, you might want to use a proper HTML parser
    return html
      .replace(/<[^>]*>/g, '') // Remove HTML tags
      .replace(/&nbsp;/g, ' ') // Replace non-breaking spaces
      .replace(/&amp;/g, '&') // Replace HTML entities
      .replace(/&lt;/g, '<')
      .replace(/&gt;/g, '>')
      .replace(/&quot;/g, '"')
      .trim();
  }

  /**
   * Clean up resources
   */
  destroy(): void {
    // Clean up the editor if needed
  }
}

/**
 * Convenience functions for one-off conversions
 * These create fresh editor instances for each conversion to avoid state issues
 */

/**
 * Convert markdown to Lexical document
 * Always returns a successful result or throws an error - no fallbacks
 */
export async function markdownToLexical(
  markdown: string,
  options?: ConversionOptions
): Promise<EnhancedConversionResult<LexicalDocument>> {
  const parser = new Markdown();
  try {
    const result = await parser.markdownToLexical(markdown, options);
    if (!result.success || !result.data) {
      throw new Error(`Markdown to Lexical conversion failed: ${result.error || 'Unknown error'}`);
    }
    return result;
  } finally {
    parser.destroy();
  }
}

/**
 * Convert Lexical document to markdown
 * Always returns a successful result or throws an error - no fallbacks
 */
export async function lexicalToMarkdown(
  document: LexicalDocument,
  options?: ConversionOptions
): Promise<EnhancedConversionResult<string>> {
  const parser = new Markdown();
  try {
    const result = await parser.lexicalToMarkdown(document, options);
    if (!result.success || result.data === undefined) {
      throw new Error(`Lexical to Markdown conversion failed: ${result.error || 'Unknown error'}`);
    }
    return result;
  } finally {
    parser.destroy();
  }
}

/**
 * Round-trip test: markdown -> lexical -> markdown
 */
export async function roundTrip(markdown: string): Promise<ConversionResult<string>> {
  const parser = new Markdown();
  try {
    const lexicalResult = await parser.markdownToLexical(markdown);
    if (!lexicalResult.success || !lexicalResult.data) {
      return {
        success: false,
        error: `Failed to convert to Lexical: ${lexicalResult.error}`,
      };
    }

    const markdownResult = await parser.lexicalToMarkdown(lexicalResult.data);
    if (!markdownResult.success || markdownResult.data === undefined) {
      return {
        success: false,
        error: `Failed to convert back to markdown: ${markdownResult.error}`,
      };
    }

    return {
      success: true,
      data: markdownResult.data,
    };
  } finally {
    parser.destroy();
  }
}
