import {
  setupE2ETestHooks,
  expectNotice,
  executeCommand,
  expectPostFile
} from '../helpers/shared-context';
import { setupTestFailureHandler } from '../helpers/test-failure-handler';
import { TestPostManager } from '../helpers/ghost-api-helpers';


import { test, expect, describe, beforeEach, afterEach } from 'vitest';

setupTestFailureHandler();

describe("Browse and import post", () => {
  const context = setupE2ETestHooks();
  let postManager: TestPostManager;

  beforeEach(async () => {
    postManager = new TestPostManager();
  });

  afterEach(async () => {
    if (postManager) {
      await postManager.cleanup();
    }
  });

  test("should import a new post", async () => {
    const testPost = await postManager.createPostFromFixture('sync-content-test.md');

    expect(testPost).toBeTruthy();
    expect(testPost.title).toBe('Sync Content Test');
    expect(testPost.slug).toBe('sync-content-test');

    // Debug: Check what was actually created in Ghost
    console.log('=== DEBUG: CREATED POST IN GHOST ===');
    console.log('Post ID:', testPost.id);
    console.log('Post title:', testPost.title);
    console.log('Post slug:', testPost.slug);
    console.log('Post status:', testPost.status);

    // Check if the post has lexical content
    if (testPost.lexical) {
      console.log('=== DEBUG: LEXICAL CONTENT IN GHOST POST ===');
      const lexicalDoc = JSON.parse(testPost.lexical);
      console.log(JSON.stringify(lexicalDoc, null, 2));

      // Check for specific content we expect
      const lexicalStr = JSON.stringify(lexicalDoc);
      console.log('=== DEBUG: LEXICAL CONTENT CHECKS ===');
      console.log('Contains javascript code:', lexicalStr.includes('function testSync'));
      console.log('Contains return statement:', lexicalStr.includes('return "Sync successful"'));
      console.log('Contains callout:', lexicalStr.includes('callout'));
      console.log('Contains info callout text:', lexicalStr.includes('This is an info callout'));
    } else {
      console.log('=== DEBUG: NO LEXICAL CONTENT IN GHOST POST ===');
      console.log('Post HTML:', testPost.html);
    }

    await context.page.waitForTimeout(2000);

    const fileExistsBefore = await context.page.evaluate(() => {
      const app = (window as any).app;
      const file = app.vault.getAbstractFileByPath('articles/sync-content-test.md');
      return file !== null;
    });
    expect(fileExistsBefore).toBe(false);

    await executeCommand(context, 'Browse and sync posts from Ghost');
    await context.page.waitForTimeout(3000);

    await context.page.waitForSelector('.ghost-post-suggestion', { timeout: 10000 });

    // Debug: Check what posts are available in the suggester
    const suggestions = await context.page.evaluate(() => {
      const suggestionElements = document.querySelectorAll('.ghost-post-suggestion');
      return Array.from(suggestionElements).map(el => el.textContent);
    });
    console.log('=== DEBUG: AVAILABLE POST SUGGESTIONS ===');
    console.log(suggestions);

    await context.page.keyboard.type('sync-content-test');
    await context.page.keyboard.press('Enter');

    // Debug: Add console listener to capture any conversion logs
    const consoleLogs: string[] = [];
    context.page.on('console', msg => {
      const text = msg.text();
      if (text.includes('LEXICAL PROCESSING') || text.includes('CONVERTING GHOST POST') || text.includes('convertGhostPostToArticle')) {
        consoleLogs.push(text);
      }
    });

    await expectNotice(context, "Synced", 15000);

    // Debug: Show any conversion logs we captured
    if (consoleLogs.length > 0) {
      console.log('=== DEBUG: CONVERSION LOGS ===');
      consoleLogs.forEach(log => console.log(log));
    }

    const fileExists = await context.page.evaluate(() => {
      const app = (window as any).app;
      const file = app.vault.getAbstractFileByPath('articles/sync-content-test.md');
      return file !== null;
    });

    expect(fileExists).toBe(true);

    const fileContent = await context.page.evaluate(() => {
      const app = (window as any).app;
      const file = app.vault.getAbstractFileByPath('articles/sync-content-test.md');
      if (file) {
        return app.vault.read(file);
      }
      return null;
    });

    expect(fileContent).toBeTruthy();

    // Debug: Show what content was actually imported
    console.log('=== DEBUG: IMPORTED FILE CONTENT ===');
    console.log(fileContent);
    console.log('=== DEBUG: CONTENT CHECKS ===');
    console.log('Contains Title:', fileContent.includes('Title: "Sync Content Test"'));
    console.log('Contains Slug:', fileContent.includes('Slug: "sync-content-test"'));
    console.log('Contains heading:', fileContent.includes('# Sync Content Test'));
    console.log('Contains javascript code block:', fileContent.includes('```javascript'));
    console.log('Contains return statement:', fileContent.includes('return "Sync successful"'));
    console.log('Contains info callout:', fileContent.includes('> [!info]'));
    console.log('Contains callout text:', fileContent.includes('> This is an info callout'));

    expect(fileContent).toContain('Title: "Sync Content Test"');
    expect(fileContent).toContain('Slug: "sync-content-test"');
    expect(fileContent).toContain('# Sync Content Test');
    expect(fileContent).toContain('```javascript');
    expect(fileContent).toContain('return "Sync successful"');
    expect(fileContent).toContain('> [!info]');
    expect(fileContent).toContain('> This is an info callout');
  });
});
