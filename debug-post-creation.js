#!/usr/bin/env node

// Debug script to check what happens when we create a post from the fixture
const fs = require('fs');
const path = require('path');
const matter = require('gray-matter');

async function debugPostCreation() {
  try {
    // Read the fixture file
    const fixturePath = path.resolve('tests/fixtures/sync-content-test.md');
    const fixtureContent = fs.readFileSync(fixturePath, 'utf8');
    
    console.log('=== ORIGINAL FIXTURE CONTENT ===');
    console.log(fixtureContent);
    console.log('\n=== PARSED FRONTMATTER AND CONTENT ===');
    
    // Parse frontmatter and content using gray-matter
    const parsed = matter(fixtureContent);
    const frontMatter = parsed.data;
    const markdownContent = parsed.content;
    
    console.log('Frontmatter:', JSON.stringify(frontMatter, null, 2));
    console.log('\nMarkdown content:');
    console.log(markdownContent);
    
    // Use ContentConverter to create Ghost post data
    const { ContentConverter } = require('./src/utils/content-converter');
    const postData = await ContentConverter.createGhostPostData(frontMatter, markdownContent);
    
    console.log('\n=== GENERATED GHOST POST DATA ===');
    console.log('Title:', postData.title);
    console.log('Slug:', postData.slug);
    console.log('Status:', postData.status);
    
    console.log('\n=== LEXICAL CONTENT ===');
    const lexicalDoc = JSON.parse(postData.lexical);
    console.log(JSON.stringify(lexicalDoc, null, 2));
    
    // Now let's try to convert it back to markdown to see what we get
    const { Markdown } = require('./src/markdown');
    const parser = new Markdown();
    
    try {
      const result = await parser.lexicalToMarkdown(lexicalDoc);
      console.log('\n=== CONVERTED BACK TO MARKDOWN ===');
      console.log(result.data);
      
      // Check if it contains the expected content
      console.log('\n=== CONTENT CHECKS ===');
      console.log('Contains ```javascript:', result.data.includes('```javascript'));
      console.log('Contains return "Sync successful":', result.data.includes('return "Sync successful"'));
      console.log('Contains > [!info]:', result.data.includes('> [!info]'));
      console.log('Contains callout text:', result.data.includes('This is an info callout'));
      
    } finally {
      parser.destroy();
    }
    
  } catch (error) {
    console.error('Error:', error);
    console.error('Stack:', error.stack);
  }
}

debugPostCreation();
